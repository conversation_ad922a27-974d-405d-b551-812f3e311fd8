package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.domain.vo.product.control.invert.DesiredAndReported;
import org.apache.commons.lang3.StringUtils;

/**
 * 通用功能工具类
 * 提供硬件模型解析等通用功能
 */
public class CommonFunctions {

    /**
     * 获取硬件模型
     * 基于序列号、设置信息和模型名称确定硬件模型
     * 优先级：desired.Settings.Inverter.ModelName > reported.Settings.Inverter.ModelName > SystemStatus.Inverter.ModelName > SerialNumber
     *
     * @param serialNumber 序列号
     * @param desiredAndReported 期望和报告的设置
     * @param modelNameFromSS 来自系统状态的模型名称
     * @return 硬件模型枚举
     */
    public static HardwareModelEnum getHardwareModel(
            String serialNumber,
            DesiredAndReported desiredAndReported,
            String modelNameFromSS) {

        // 使用优先级顺序获取硬件模型：desired -> reported -> SystemStatus
        HardwareModelEnum hardwareModel = getHardwareModel(desiredAndReported, modelNameFromSS);

        if (hardwareModel == HardwareModelEnum.Unknown) {
            // 注册页面会进入这里
            // 没有期望或报告的模型名称，也没有收到有效的系统状态
            // 如果系统还没有被使用，解析序列号来识别模型指定
            hardwareModel = HardwareModelHelpers.determineFromSerialNumber(serialNumber);
        }

        return hardwareModel;
    }

    /**
     * 从期望设置 -> 报告设置 -> 系统状态中解析硬件模型
     *
     * @param desiredAndReported 当前设置（期望和报告）
     * @param modelNameFromSS 来自系统状态的模型名称
     * @return 要使用的模型名称
     */
    private static HardwareModelEnum getHardwareModel(DesiredAndReported desiredAndReported, String modelNameFromSS) {
        String modelNameToUse = null;

        // 优先级1：检查期望设置中的逆变器模型名称
        if (desiredAndReported != null &&
            desiredAndReported.getDesired() != null &&
            desiredAndReported.getDesired().getInverter() != null &&
            !isNullOrEmpty(desiredAndReported.getDesired().getInverter().getModelName())) {
            modelNameToUse = desiredAndReported.getDesired().getInverter().getModelName();
        }
        // 优先级2：检查报告设置中的逆变器模型名称
        else if (desiredAndReported != null &&
                 desiredAndReported.getReported() != null &&
                 desiredAndReported.getReported().getInverterSettings() != null &&
                 !isNullOrEmpty(desiredAndReported.getReported().getInverterSettings().getModelName())) {
            modelNameToUse = desiredAndReported.getReported().getInverterSettings().getModelName();
        }
        // 优先级3：使用系统状态中的模型名称
        else {
            modelNameToUse = modelNameFromSS;
        }

        return HardwareModelHelpers.parseModelName(modelNameToUse);
    }
    
    /**
     * 检查字符串是否为空或null
     * 
     * @param str 要检查的字符串
     * @return 如果字符串为null或空则返回true
     */
    public static boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 安全地从固件版本字符串中提取ARM版本
     * 
     * @param firmwareVersion 固件版本字符串
     * @return ARM版本号，如果解析失败返回0
     */
    public static int extractArmVersionSafely(String firmwareVersion) {
        if (isNullOrEmpty(firmwareVersion)) {
            return 0;
        }
        
        try {
            return ArmAndDspVersions.getOrZeros(firmwareVersion).getArmVersion();
        } catch (Exception e) {
            return 0;
        }
    }
}
